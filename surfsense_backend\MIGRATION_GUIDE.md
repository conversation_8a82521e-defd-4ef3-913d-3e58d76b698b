# Migration Guide: Add search_space_id and user_id to chats table

## Tổng quan
Migration này thêm hai cột mới vào bảng `chats`:
- `search_space_id`: Liên kết chat với search space (bắt buộc)
- `user_id`: <PERSON><PERSON>n kết chat với user (bắ<PERSON> buộc)

## Files đã tạo/sửa đổi

### 1. Migration files
- `alembic/versions/12_merge_heads.py`: Merge multiple heads
- `alembic/versions/13_add_search_space_id_and_user_id_to_chats.py`: Thêm các cột mới

### 2. Model updates
- `app/db.py`: Cập nhật class `Chat` và `User` để thêm relationships

### 3. Helper scripts
- `run_migration.py`: Script để chạy migration
- `MIGRATION_GUIDE.md`: Hướng dẫn này

## Cách chạy migration

### Phương pháp 1: Sử dụng script helper
```bash
cd surfsense_backend
python run_migration.py
```

### Phương pháp 2: <PERSON><PERSON><PERSON> trực tiếp với alemb<PERSON>
```bash
cd surfsense_backend

# Kiểm tra trạng thái hiện tại
python -m alembic current

# Kiểm tra multiple heads (nếu có)
python -m alembic heads

# Nếu có multiple heads, merge trước
python -m alembic upgrade heads

# Chạy migration
python -m alembic upgrade head

# Kiểm tra lại trạng thái
python -m alembic current
```

### Phương pháp 3: Nếu alembic không hoạt động
```bash
cd surfsense_backend

# Cài đặt dependencies nếu cần
pip install alembic sqlalchemy asyncpg

# Chạy migration
python -m alembic upgrade head
```

## Kiểm tra migration

Sau khi chạy migration, bạn có thể kiểm tra bằng cách:

1. **Kiểm tra cấu trúc bảng trong database:**
```sql
\d chats
```

2. **Kiểm tra foreign keys:**
```sql
SELECT 
    tc.table_name, 
    kcu.column_name, 
    ccu.table_name AS foreign_table_name,
    ccu.column_name AS foreign_column_name 
FROM 
    information_schema.table_constraints AS tc 
    JOIN information_schema.key_column_usage AS kcu
      ON tc.constraint_name = kcu.constraint_name
    JOIN information_schema.constraint_column_usage AS ccu
      ON ccu.constraint_name = tc.constraint_name
WHERE tc.constraint_type = 'FOREIGN KEY' AND tc.table_name='chats';
```

## Rollback (nếu cần)

Nếu cần rollback migration:
```bash
cd surfsense_backend
python -m alembic downgrade -1
```

## Lưu ý quan trọng

1. **Backup database** trước khi chạy migration
2. **Đảm bảo database đang chạy** và có thể kết nối
3. **Kiểm tra connection string** trong `alembic.ini`
4. **Cập nhật dữ liệu hiện có**: Nếu bảng `chats` đã có dữ liệu, bạn cần cập nhật các giá trị `search_space_id` và `user_id` cho các record hiện có

## Cập nhật dữ liệu hiện có (nếu cần)

Nếu bảng `chats` đã có dữ liệu, bạn cần chạy script để cập nhật các giá trị mới:

```sql
-- Ví dụ: Cập nhật tất cả chats với search_space_id và user_id mặc định
-- (Thay đổi theo logic business của bạn)

UPDATE chats 
SET 
    search_space_id = (SELECT id FROM searchspaces LIMIT 1),
    user_id = (SELECT id FROM "user" LIMIT 1)
WHERE search_space_id IS NULL OR user_id IS NULL;
```

## Troubleshooting

### Lỗi "Multiple heads are present"
Nếu gặp lỗi này khi tạo migration mới:
```bash
# Kiểm tra các heads hiện tại
python -m alembic heads

# Merge các heads
python -m alembic upgrade heads

# Sau đó chạy migration bình thường
python -m alembic upgrade head
```

### Lỗi "column already exists"
Nếu gặp lỗi này, có thể cột đã tồn tại. Kiểm tra cấu trúc bảng:
```sql
\d chats
```

### Lỗi foreign key constraint
Đảm bảo rằng:
- Bảng `searchspaces` và `user` tồn tại
- Có dữ liệu trong các bảng này nếu bảng `chats` đã có dữ liệu

### Lỗi connection
Kiểm tra:
- Database đang chạy
- Connection string trong `alembic.ini` đúng
- User có quyền truy cập database
