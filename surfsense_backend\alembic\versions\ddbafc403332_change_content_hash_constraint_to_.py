"""change_content_hash_constraint_to_composite

Revision ID: ddbafc403332
Revises: c1c51ec182c1
Create Date: 2025-06-12 19:24:50.591605

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = 'ddbafc403332'
down_revision: Union[str, None] = 'c1c51ec182c1'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    pass


def downgrade() -> None:
    """Downgrade schema."""
    pass
