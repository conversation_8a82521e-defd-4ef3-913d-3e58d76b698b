"""Add search_space_id and user_id columns to chats table

Revision ID: 13
Revises: 12
"""

from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects.postgresql import UUID


# revision identifiers, used by Alembic.
revision: str = "13"
down_revision: Union[str, None] = "12"
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema - add search_space_id and user_id columns to chats table."""
    
    # Add search_space_id column
    op.add_column('chats', sa.Column('search_space_id', sa.Integer(), nullable=False))
    
    # Add user_id column
    op.add_column('chats', sa.Column('user_id', UUID(as_uuid=True), nullable=False))
    
    # Create foreign key constraint for search_space_id
    op.create_foreign_key(
        op.f('fk_chats_search_space_id_searchspaces'), 
        'chats', 
        'searchspaces', 
        ['search_space_id'], 
        ['id'], 
        ondelete='CASCADE'
    )
    
    # Create foreign key constraint for user_id
    op.create_foreign_key(
        op.f('fk_chats_user_id_user'), 
        'chats', 
        'user', 
        ['user_id'], 
        ['id'], 
        ondelete='CASCADE'
    )


def downgrade() -> None:
    """Downgrade schema - remove search_space_id and user_id columns from chats table."""
    
    # Drop foreign key constraints
    op.drop_constraint(op.f('fk_chats_user_id_user'), 'chats', type_='foreignkey')
    op.drop_constraint(op.f('fk_chats_search_space_id_searchspaces'), 'chats', type_='foreignkey')
    
    # Drop columns
    op.drop_column('chats', 'user_id')
    op.drop_column('chats', 'search_space_id')
