"""Add GITHUB_CONNECTOR to SearchSourceConnectorType enum

Revision ID: 1
Revises: 

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
# Import pgvector if needed for other types, though not for this ENUM change
# import pgvector


# revision identifiers, used by Alembic.
revision: str = '1'
down_revision: Union[str, None] = None
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###

    # Check if GITHUB_CONNECTOR already exists before adding it
    connection = op.get_bind()
    result = connection.execute(
        "SELECT 1 FROM pg_enum WHERE enumlabel = 'GITHUB_CONNECTOR' AND enumtypid = (SELECT oid FROM pg_type WHERE typname = 'searchsourceconnectortype')"
    ).fetchone()

    if not result:
        op.execute("ALTER TYPE searchsourceconnectortype ADD VALUE 'GITHUB_CONNECTOR'")

    # Pass for the rest, as autogenerate didn't run to add other schema details
    pass
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    
    # Downgrading removal of an enum value is complex and potentially dangerous
    # if the value is in use. Often omitted or requires manual SQL based on context.
    # For now, we'll just pass. If you needed to reverse this, you'd likely 
    # have to manually check if 'GITHUB_CONNECTOR' is used in the table
    # and then potentially recreate the type without it.
    op.execute("ALTER TYPE searchsourceconnectortype RENAME TO searchsourceconnectortype_old")
    op.execute("CREATE TYPE searchsourceconnectortype AS ENUM('SERPER_API', 'TAVILY_API', 'SLACK_CONNECTOR', 'NOTION_CONNECTOR')")
    op.execute((
        "ALTER TABLE search_source_connectors ALTER COLUMN connector_type TYPE searchsourceconnectortype USING "
        "connector_type::text::searchsourceconnectortype"
    ))
    op.execute("DROP TYPE searchsourceconnectortype_old")

    
    pass
    # ### end Alembic commands ### 
