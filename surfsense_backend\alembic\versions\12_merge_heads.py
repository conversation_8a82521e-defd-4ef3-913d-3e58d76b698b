"""Merge multiple heads

Revision ID: 12
Revises: 11, e55302644c51
"""

from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = "12"
down_revision: Union[str, None] = None
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = ("11", "e55302644c51")


def upgrade() -> None:
    """Merge heads - no changes needed."""
    pass


def downgrade() -> None:
    """Downgrade merge - no changes needed."""
    pass
