"""change_content_hash_constraint_to_composite

Revision ID: c1c51ec182c1
Revises: 11
Create Date: 2025-06-12 19:24:38.838578

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = 'c1c51ec182c1'
down_revision: Union[str, None] = '11'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    pass


def downgrade() -> None:
    """Downgrade schema."""
    pass
